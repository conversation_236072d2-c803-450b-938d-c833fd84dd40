-- 为现有MySQL表添加download字段的SQL脚本
-- 如果表已经存在且没有download字段，需要手动执行这些SQL语句

-- 检查生产环境表是否存在download字段
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'channel_data' 
  AND COLUMN_NAME = 'download';

-- 如果上面的查询没有返回结果，说明download字段不存在，需要添加
-- 为生产环境表添加download字段
ALTER TABLE channel_data 
ADD COLUMN download BIGINT DEFAULT 0 
AFTER click;

-- 检查测试环境表是否存在download字段
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'channel_data_test' 
  AND COLUMN_NAME = 'download';

-- 如果上面的查询没有返回结果，说明download字段不存在，需要添加
-- 为测试环境表添加download字段
ALTER TABLE channel_data_test 
ADD COLUMN download BIGINT DEFAULT 0 
AFTER click;

-- 验证字段是否添加成功
DESCRIBE channel_data;
DESCRIBE channel_data_test;

-- 查看表结构
SHOW CREATE TABLE channel_data;
SHOW CREATE TABLE channel_data_test;
