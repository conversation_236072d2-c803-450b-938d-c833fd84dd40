# 测试环境使用说明

## 概述

现在系统支持同时运行生产环境和测试环境，两套环境完全独立：

- **生产环境**: 连接生产Kafka，数据存储到生产日志、生产SQLite和生产MySQL表
- **测试环境**: 连接测试Kafka，数据存储到测试日志、测试SQLite和测试MySQL表

## 配置说明

### 环境变量配置

在 `.env` 文件中已添加以下测试环境配置：

```bash
# 测试环境Kafka配置
KAFKA_BOOTSTRAP_SERVERS_TEST=192.168.1.20:9092,192.168.14.241:9092,192.168.14.200:9092

# 测试环境日志目录
LOG_DIR_TEST=logs_test

# 测试环境SQLite数据库
DATABASE_PATH_TEST=kafka_data_test.db

# 测试环境MySQL表
MYSQL_TABLE_TEST=channel_data_test
```

### 目录结构

```
项目根目录/
├── logs/                    # 生产环境日志目录
│   └── kafka_data.log      # 生产环境日志文件
├── logs_test/              # 测试环境日志目录
│   └── kafka_data_test.log # 测试环境日志文件
├── kafka_data.db           # 生产环境SQLite数据库
├── kafka_data_test.db      # 测试环境SQLite数据库
└── ...
```

## 数据流向

### 生产环境数据流
```
生产Kafka → 生产日志(logs/kafka_data.log) → 生产SQLite(kafka_data.db) → 生产MySQL表(channel_data)
```

### 测试环境数据流
```
测试Kafka → 测试日志(logs_test/kafka_data_test.log) → 测试SQLite(kafka_data_test.db) → 测试MySQL表(channel_data_test)
```

## API接口

### 生产环境API
- `GET /` - 服务状态
- `GET /stats` - 生产环境统计信息
- `GET /messages` - 生产环境消息列表
- `GET /import` - 手动触发生产环境日志导入
- `GET /sync-mysql` - 手动触发生产环境MySQL同步

### 测试环境API
- `GET /stats-test` - 测试环境统计信息
- `GET /messages-test` - 测试环境消息列表
- `GET /import-test` - 手动触发测试环境日志导入
- `GET /sync-mysql-test` - 手动触发测试环境MySQL同步

## 运行线程

系统启动后会运行以下线程：

### 生产环境线程
1. **kafka_consumer_thread** - 生产环境Kafka消费者
2. **auto_import_thread** - 生产环境自动日志导入
3. **mysql_sync_thread** - 生产环境MySQL同步

### 测试环境线程
1. **kafka_consumer_thread_test** - 测试环境Kafka消费者
2. **auto_import_thread_test** - 测试环境自动日志导入
3. **mysql_sync_thread_test** - 测试环境MySQL同步

## 使用方法

### 1. 启动服务

```bash
# 方法1: 直接启动
python app.py

# 方法2: 使用systemd服务
sudo systemctl start kafka-data-service
```

### 2. 检查服务状态

```bash
# 检查配置
python test_config.py

# 测试启动
python test_start.py

# 测试API
python test_api.py
```

### 3. 查看日志

```bash
# 生产环境日志
tail -f logs/kafka_data.log

# 测试环境日志
tail -f logs_test/kafka_data_test.log
```

### 4. 查看数据库

```bash
# 生产环境SQLite
sqlite3 kafka_data.db "SELECT COUNT(*) FROM messages;"

# 测试环境SQLite
sqlite3 kafka_data_test.db "SELECT COUNT(*) FROM messages;"
```

### 5. 查看MySQL表

```sql
-- 生产环境表
SELECT COUNT(*) FROM channel_data;

-- 测试环境表
SELECT COUNT(*) FROM channel_data_test;
```

## 监控和维护

### 日志监控
- 生产环境日志: `logs/kafka_data.log`
- 测试环境日志: `logs_test/kafka_data_test.log`

### 数据库监控
- 通过API接口查看统计信息
- 直接查询SQLite和MySQL数据库

### 故障排除
1. 检查Kafka连接状态
2. 查看日志文件中的错误信息
3. 验证MySQL连接和表结构
4. 检查磁盘空间和权限

## 注意事项

1. **独立运行**: 生产环境和测试环境完全独立，互不影响
2. **资源消耗**: 同时运行两套环境会增加系统资源消耗
3. **数据隔离**: 确保测试数据不会影响生产数据
4. **配置管理**: 测试环境配置是可选的，如果不配置则不会启动测试环境线程
5. **监控告警**: 建议对两套环境分别设置监控和告警
