# 添加新渠道示例

## 重构优势

经过重构后，现在添加新渠道只需要修改一个函数：`process_messages_for_mysql()`

## 添加新渠道的步骤

### 1. 在 `process_messages_for_mysql()` 函数中添加新渠道处理逻辑

找到 `app.py` 文件中的 `process_messages_for_mysql()` 函数，在现有的渠道处理逻辑后添加新渠道：

```python
def process_messages_for_mysql(rows, mysql_cursor, mysql_table, logger_instance, env_name=""):
    # ... 现有代码 ...
    
    for row in rows:
        try:
            # 解析JSON数据
            message_value = json.loads(row["value"])
            
            # 提取渠道
            channel = message_value.get("channel", "")
            
            # 根据不同渠道处理不同的数据结构
            if channel == "honor":
                # 荣耀渠道处理逻辑...
                
            elif channel == "oppo":
                # OPPO渠道处理逻辑...
                
            elif channel == "xiaomi":  # 新增小米渠道
                # 小米渠道处理逻辑
                data_obj = message_value.get("data", {})
                campaigns_data = data_obj.get("campaigns", [])
                
                for creative_data in campaigns_data:
                    creative_id = creative_data.get("creative_id")
                    creative_name = creative_data.get("creative_name")
                    campaign_id = creative_data.get("campaign_id")
                    campaign_name = creative_data.get("campaign_name")
                    cost = float(creative_data.get("spend", 0))  # 小米直接是元
                    impression = int(creative_data.get("impressions", 0))
                    click = int(creative_data.get("clicks", 0))
                    
                    # 处理时间
                    date_str = creative_data.get("date", "")
                    if date_str:
                        stat_time = f"{date_str} 00:00:00"
                        ds = date_str
                    else:
                        stat_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        ds = datetime.now().strftime('%Y-%m-%d')
                    
                    # 插入到MySQL
                    mysql_cursor.execute(f'''
                    INSERT IGNORE INTO {mysql_table}
                    (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ''', (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds))
                    
                    if mysql_cursor.rowcount > 0:
                        synced_count += 1
                        
            else:
                # 其他渠道暂时不处理
                logger_instance.debug(f"{env_name}跳过未知渠道: {channel}")
```

### 2. 测试新渠道

创建测试数据验证新渠道处理逻辑：

```python
# 小米渠道测试数据
xiaomi_message = {
    "channel": "xiaomi",
    "data": {
        "campaigns": [
            {
                "creative_id": "xiaomi_creative_001",
                "creative_name": "小米测试创意",
                "campaign_id": "xiaomi_campaign_001", 
                "campaign_name": "小米测试广告计划",
                "spend": 88.88,  # 直接是元
                "impressions": 3000,
                "clicks": 150,
                "date": "2025-07-23"
            }
        ]
    }
}
```

### 3. 完整示例

以下是一个完整的新渠道添加示例：

```python
elif channel == "vivo":  # 新增vivo渠道
    # vivo渠道数据结构
    data_obj = message_value.get("data", {})
    ad_list = data_obj.get("adList", [])
    
    for ad_data in ad_list:
        # 提取字段
        creative_id = ad_data.get("adId")
        creative_name = ad_data.get("adName")
        campaign_id = ad_data.get("planId")
        campaign_name = ad_data.get("planName")
        
        # vivo的费用单位是厘，需要除以1000转换为元
        cost = float(ad_data.get("consume", 0)) / 1000
        impression = int(ad_data.get("show", 0))
        click = int(ad_data.get("click", 0))
        
        # 处理时间字段
        report_date = ad_data.get("reportDate", "")
        if report_date:
            # vivo时间格式: "20250723"
            stat_time = datetime.strptime(report_date, "%Y%m%d").strftime('%Y-%m-%d %H:%M:%S')
            ds = datetime.strptime(report_date, "%Y%m%d").strftime('%Y-%m-%d')
        else:
            stat_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ds = datetime.now().strftime('%Y-%m-%d')
        
        # 插入到MySQL
        mysql_cursor.execute(f'''
        INSERT IGNORE INTO {mysql_table}
        (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ''', (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds))
        
        if mysql_cursor.rowcount > 0:
            synced_count += 1
```

## 重构前后对比

### 重构前（需要修改两个函数）
- `sync_to_mysql()` - 生产环境同步函数
- `sync_to_mysql_test()` - 测试环境同步函数

### 重构后（只需修改一个函数）
- `process_messages_for_mysql()` - 通用消息处理函数

## 优势总结

1. **代码复用**: 消除了重复代码，提高了代码质量
2. **维护简单**: 添加新渠道只需修改一个地方
3. **一致性**: 生产环境和测试环境使用相同的处理逻辑
4. **扩展性**: 更容易添加新的渠道支持
5. **测试友好**: 可以独立测试消息处理逻辑

## 注意事项

1. **数据格式**: 每个渠道的数据格式可能不同，需要仔细分析
2. **字段映射**: 确保正确映射到统一的数据库字段
3. **单位转换**: 注意不同渠道的金额单位（元、分、厘、微分等）
4. **时间格式**: 处理不同渠道的时间格式差异
5. **错误处理**: 添加适当的异常处理和日志记录

通过这种重构，您现在可以更轻松地添加新渠道，而不需要担心在多个地方重复相同的逻辑！
