# -*- coding: utf-8 -*-
"""
File: app.py
Author: Sheng
Date: 2025-07-14
Description: None
"""



from starlite import Starlite, get
from kafka import KafkaConsumer
import threading
import logging
import time
import os
import json
from datetime import datetime
import sqlite3
from logging.handlers import TimedRotatingFileHandler
from dotenv import load_dotenv
from typing import Dict, Any
import mysql.connector
from mysql.connector import Error as MySQLError

# 加载环境变量
load_dotenv()

# 获取配置 - 移除敏感信息的默认值
KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "").split(",") if os.getenv("KAFKA_BOOTSTRAP_SERVERS") else []
KAFKA_BOOTSTRAP_SERVERS_TEST = os.getenv("KAFKA_BOOTSTRAP_SERVERS_TEST", "").split(",") if os.getenv("KAFKA_BOOTSTRAP_SERVERS_TEST") else []
KAFKA_TOPIC = os.getenv("KAFKA_TOPIC", "ad_channel_data_sync")
KAFKA_GROUP_ID = os.getenv("KAFKA_GROUP_ID", "starlite-service-group")
KAFKA_AUTO_OFFSET_RESET = os.getenv("KAFKA_AUTO_OFFSET_RESET", "earliest")
KAFKA_SECURITY_PROTOCOL = os.getenv("KAFKA_SECURITY_PROTOCOL", "PLAINTEXT")

LOG_DIR = os.getenv("LOG_DIR", "logs")
LOG_DIR_TEST = os.getenv("LOG_DIR_TEST", "logs_test")
LOG_RETENTION_DAYS = int(os.getenv("LOG_RETENTION_DAYS", "7"))

DATABASE_PATH = os.getenv("DATABASE_PATH", "kafka_data.db")
DATABASE_PATH_TEST = os.getenv("DATABASE_PATH_TEST", "kafka_data_test.db")

# 自动导入配置
AUTO_IMPORT_ENABLED = os.getenv("AUTO_IMPORT_ENABLED", "true").lower() == "true"
AUTO_IMPORT_INTERVAL = int(os.getenv("AUTO_IMPORT_INTERVAL", "300"))  # 默认5分钟

# MySQL配置 - 不提供默认值，必须在.env中配置
MYSQL_HOST = os.getenv("MYSQL_HOST")
MYSQL_PORT = int(os.getenv("MYSQL_PORT", "3306"))
MYSQL_USER = os.getenv("MYSQL_USER")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD")
MYSQL_DATABASE = os.getenv("MYSQL_DATABASE")
MYSQL_TABLE = os.getenv("MYSQL_TABLE", "channel_data")
MYSQL_TABLE_TEST = os.getenv("MYSQL_TABLE_TEST", "channel_data_test")

# MySQL同步配置
MYSQL_SYNC_ENABLED = os.getenv("MYSQL_SYNC_ENABLED", "true").lower() == "true"
MYSQL_SYNC_INTERVAL = int(os.getenv("MYSQL_SYNC_INTERVAL", "600"))  # 默认10分钟

# 配置验证
def validate_config():
    """验证必要的配置项"""
    errors = []

    if not KAFKA_BOOTSTRAP_SERVERS or KAFKA_BOOTSTRAP_SERVERS == ['']:
        errors.append("KAFKA_BOOTSTRAP_SERVERS 未配置")

    if MYSQL_SYNC_ENABLED:
        if not MYSQL_HOST:
            errors.append("MYSQL_HOST 未配置")
        if not MYSQL_USER:
            errors.append("MYSQL_USER 未配置")
        if not MYSQL_PASSWORD:
            errors.append("MYSQL_PASSWORD 未配置")
        if not MYSQL_DATABASE:
            errors.append("MYSQL_DATABASE 未配置")

    # 验证测试环境配置（可选）
    if KAFKA_BOOTSTRAP_SERVERS_TEST and KAFKA_BOOTSTRAP_SERVERS_TEST != ['']:
        logger.info("检测到测试环境Kafka配置，将启动测试环境服务")
        logger_test.info(f"测试环境Kafka服务器: {KAFKA_BOOTSTRAP_SERVERS_TEST}")

    if errors:
        logger.error("配置验证失败:")
        for error in errors:
            logger.error(f"  - {error}")
        logger.error("请检查 .env 文件中的配置")
        return False

    return True

# 配置生产环境日志
os.makedirs(LOG_DIR, exist_ok=True)
logger = logging.getLogger("kafka_service")
logger.setLevel(logging.INFO)

# 创建生产环境文件处理器
log_file = os.path.join(LOG_DIR, "kafka_data.log")
file_handler = TimedRotatingFileHandler(
    log_file,
    when='midnight',
    interval=1,
    backupCount=LOG_RETENTION_DAYS,
    encoding='utf-8'
)
file_handler.setLevel(logging.INFO)

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# 创建格式器
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# 添加处理器到logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 配置测试环境日志
os.makedirs(LOG_DIR_TEST, exist_ok=True)
logger_test = logging.getLogger("kafka_service_test")
logger_test.setLevel(logging.INFO)

# 创建测试环境文件处理器
log_file_test = os.path.join(LOG_DIR_TEST, "kafka_data_test.log")
file_handler_test = TimedRotatingFileHandler(
    log_file_test,
    when='midnight',
    interval=1,
    backupCount=LOG_RETENTION_DAYS,
    encoding='utf-8'
)
file_handler_test.setLevel(logging.INFO)

# 测试环境也使用相同的控制台处理器和格式器
file_handler_test.setFormatter(formatter)

# 添加处理器到测试logger
logger_test.addHandler(file_handler_test)
logger_test.addHandler(console_handler)

# 初始化生产环境SQLite数据库
def init_db():
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # 创建消息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            topic TEXT,
            partition INTEGER,
            offset INTEGER,
            key TEXT,
            value TEXT,
            timestamp TEXT,
            receive_time TEXT
        )
        ''')

        # 创建同步记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS synced_records (
            id INTEGER PRIMARY KEY,
            synced INTEGER DEFAULT 0
        )
        ''')

        conn.commit()
        logger.info("生产环境SQLite数据库初始化成功")
    except Exception as e:
        logger.error(f"生产环境SQLite初始化失败: {str(e)}")
    finally:
        if conn:
            conn.close()

# 初始化测试环境SQLite数据库
def init_db_test():
    try:
        conn = sqlite3.connect(DATABASE_PATH_TEST)
        cursor = conn.cursor()

        # 创建消息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            topic TEXT,
            partition INTEGER,
            offset INTEGER,
            key TEXT,
            value TEXT,
            timestamp TEXT,
            receive_time TEXT
        )
        ''')

        # 创建同步记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS synced_records (
            id INTEGER PRIMARY KEY,
            synced INTEGER DEFAULT 0
        )
        ''')

        conn.commit()
        logger_test.info("测试环境SQLite数据库初始化成功")
    except Exception as e:
        logger_test.error(f"测试环境SQLite初始化失败: {str(e)}")
    finally:
        if conn:
            conn.close()

# 初始化生产环境MySQL数据库
def init_mysql():
    if not MYSQL_SYNC_ENABLED:
        return

    conn = None
    cursor = None
    try:
        conn = mysql.connector.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DATABASE
        )
        cursor = conn.cursor()

        # 创建生产环境表
        cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS {MYSQL_TABLE} (
            id INT AUTO_INCREMENT PRIMARY KEY,
            channel VARCHAR(255),
            campaign_id VARCHAR(255),
            campaign_name VARCHAR(255),
            creative_id VARCHAR(255),
            creative_name VARCHAR(255),
            cost DOUBLE,
            impression BIGINT,
            click BIGINT,
            download BIGINT DEFAULT 0,
            stat_time DATETIME,
            ds DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY channel_creative_time (channel, creative_id, stat_time)
        )
        ''')
        conn.commit()
        logger.info(f"生产环境MySQL表 {MYSQL_TABLE} 初始化成功")
    except MySQLError as e:
        logger.error(f"生产环境MySQL初始化失败: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# 初始化测试环境MySQL数据库
def init_mysql_test():
    if not MYSQL_SYNC_ENABLED:
        return

    conn = None
    cursor = None
    try:
        conn = mysql.connector.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DATABASE
        )
        cursor = conn.cursor()

        # 创建测试环境表
        cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS {MYSQL_TABLE_TEST} (
            id INT AUTO_INCREMENT PRIMARY KEY,
            channel VARCHAR(255),
            campaign_id VARCHAR(255),
            campaign_name VARCHAR(255),
            creative_id VARCHAR(255),
            creative_name VARCHAR(255),
            cost DOUBLE,
            impression BIGINT,
            click BIGINT,
            download BIGINT DEFAULT 0,
            stat_time DATETIME,
            ds DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY channel_creative_time (channel, creative_id, stat_time)
        )
        ''')
        conn.commit()
        logger_test.info(f"测试环境MySQL表 {MYSQL_TABLE_TEST} 初始化成功")
    except MySQLError as e:
        logger_test.error(f"测试环境MySQL初始化失败: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Kafka生产环境消费者线程
def kafka_consumer_thread():
    while True:  # 添加外层循环确保持续运行
        try:
            logger.info(f"尝试连接Kafka服务器: {KAFKA_BOOTSTRAP_SERVERS}")
            logger.info(f"Kafka配置 - 主题: {KAFKA_TOPIC}, 组ID: {KAFKA_GROUP_ID}, 偏移重置: {KAFKA_AUTO_OFFSET_RESET}")

            consumer = KafkaConsumer(
                KAFKA_TOPIC,
                bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
                auto_offset_reset=KAFKA_AUTO_OFFSET_RESET,
                group_id=KAFKA_GROUP_ID,
                security_protocol=KAFKA_SECURITY_PROTOCOL,
                consumer_timeout_ms=30000,  # 30秒超时，用于心跳检测
                enable_auto_commit=True,
                auto_commit_interval_ms=1000,
                session_timeout_ms=30000,
                heartbeat_interval_ms=10000
            )

            logger.info(f"Kafka消费者已启动，连接到 {KAFKA_BOOTSTRAP_SERVERS}，订阅主题 {KAFKA_TOPIC}")

            # 添加心跳检测
            last_heartbeat = time.time()

            while True:  # 内层循环处理消息
                try:
                    # 使用poll方法而不是for循环，这样可以更好地控制超时
                    message_batch = consumer.poll(timeout_ms=30000)

                    current_time = time.time()
                    if current_time - last_heartbeat > 30:  # 每30秒输出一次心跳
                        logger.info(f"Kafka消费者心跳检测 - 等待消息中...")
                        last_heartbeat = current_time

                    if message_batch:
                        for topic_partition, messages in message_batch.items():
                            for msg in messages:
                                try:
                                    # 记录消息到日志
                                    receive_time = datetime.now().isoformat()
                                    message_data = {
                                        "topic": msg.topic,
                                        "partition": msg.partition,
                                        "offset": msg.offset,
                                        "key": msg.key.decode('utf-8') if msg.key else None,
                                        "value": msg.value.decode('utf-8'),
                                        "timestamp": datetime.fromtimestamp(msg.timestamp/1000).isoformat(),
                                        "receive_time": receive_time
                                    }
                                    logger.info(f"接收到消息: {json.dumps(message_data)}")
                                    last_heartbeat = current_time  # 重置心跳时间

                                except Exception as e:
                                    logger.error(f"处理消息时出错: {str(e)}")

                except Exception as e:
                    logger.error(f"轮询消息时出错: {str(e)}")
                    break  # 跳出内层循环，重新连接

        except Exception as e:
            logger.error(f"Kafka消费者线程出错: {str(e)}")
            logger.info("10秒后重新启动Kafka消费者...")
            time.sleep(10)  # 等待10秒后重试

# Kafka测试环境消费者线程
def kafka_consumer_thread_test():
    while True:  # 添加外层循环确保持续运行
        try:
            logger_test.info(f"尝试连接测试Kafka服务器: {KAFKA_BOOTSTRAP_SERVERS_TEST}")
            logger_test.info(f"测试Kafka配置 - 主题: {KAFKA_TOPIC}, 组ID: {KAFKA_GROUP_ID}_test, 偏移重置: {KAFKA_AUTO_OFFSET_RESET}")

            consumer = KafkaConsumer(
                KAFKA_TOPIC,
                bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS_TEST,
                auto_offset_reset=KAFKA_AUTO_OFFSET_RESET,
                group_id=f"{KAFKA_GROUP_ID}_test",  # 使用不同的组ID避免冲突
                security_protocol=KAFKA_SECURITY_PROTOCOL,
                consumer_timeout_ms=30000,  # 30秒超时，用于心跳检测
                enable_auto_commit=True,
                auto_commit_interval_ms=1000,
                session_timeout_ms=30000,
                heartbeat_interval_ms=10000
            )

            logger_test.info(f"测试Kafka消费者已启动，连接到 {KAFKA_BOOTSTRAP_SERVERS_TEST}，订阅主题 {KAFKA_TOPIC}")

            # 添加心跳检测
            last_heartbeat = time.time()

            while True:  # 内层循环处理消息
                try:
                    # 使用poll方法而不是for循环，这样可以更好地控制超时
                    message_batch = consumer.poll(timeout_ms=30000)

                    current_time = time.time()
                    if current_time - last_heartbeat > 30:  # 每30秒输出一次心跳
                        logger_test.info(f"测试Kafka消费者心跳检测 - 等待消息中...")
                        last_heartbeat = current_time

                    if message_batch:
                        for topic_partition, messages in message_batch.items():
                            for msg in messages:
                                try:
                                    # 记录消息到测试日志
                                    receive_time = datetime.now().isoformat()
                                    message_data = {
                                        "topic": msg.topic,
                                        "partition": msg.partition,
                                        "offset": msg.offset,
                                        "key": msg.key.decode('utf-8') if msg.key else None,
                                        "value": msg.value.decode('utf-8'),
                                        "timestamp": datetime.fromtimestamp(msg.timestamp/1000).isoformat(),
                                        "receive_time": receive_time
                                    }
                                    logger_test.info(f"接收到测试消息: {json.dumps(message_data)}")
                                    last_heartbeat = current_time  # 重置心跳时间

                                except Exception as e:
                                    logger_test.error(f"处理测试消息时出错: {str(e)}")

                except Exception as e:
                    logger_test.error(f"轮询测试消息时出错: {str(e)}")
                    break  # 跳出内层循环，重新连接

        except Exception as e:
            logger_test.error(f"测试Kafka消费者线程出错: {str(e)}")
            logger_test.info("10秒后重新启动测试Kafka消费者...")
            time.sleep(10)  # 等待10秒后重试

# 通用的日志导入函数
def import_logs_to_db_common(database_path, log_dir, log_filename, message_pattern, logger_instance, env_name=""):
    """
    通用的日志导入函数

    Args:
        database_path: SQLite数据库路径
        log_dir: 日志目录
        log_filename: 日志文件名
        message_pattern: 消息匹配模式
        logger_instance: 日志记录器实例
        env_name: 环境名称（用于日志显示）
    """
    try:
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()

        # 获取日志文件
        log_file = os.path.join(log_dir, log_filename)

        if not os.path.exists(log_file):
            logger_instance.warning(f"{env_name}日志文件不存在: {log_file}")
            return

        logger_instance.info(f"{env_name}开始从日志文件导入数据: {log_file}")
        imported_count = 0
        skipped_count = 0

        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                # 只处理包含指定消息模式的行，跳过错误日志
                if message_pattern in line and "ERROR" not in line:
                    try:
                        # 提取JSON部分
                        parts = line.split(f"{message_pattern}: ")
                        if len(parts) < 2:
                            skipped_count += 1
                            continue

                        json_str = parts[1].strip()
                        data = json.loads(json_str)

                        # 使用INSERT OR IGNORE来自动跳过重复记录
                        cursor.execute('''
                        INSERT OR IGNORE INTO messages (topic, partition, offset, key, value, timestamp, receive_time)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            data["topic"],
                            data["partition"],
                            data["offset"],
                            data["key"],
                            data["value"],
                            data["timestamp"],
                            data.get("receive_time", data["timestamp"])
                        ))

                        if cursor.rowcount > 0:
                            imported_count += 1
                    except Exception as e:
                        skipped_count += 1
                        logger_instance.error(f"{env_name}解析日志行时出错: {str(e)}, 行内容: {line[:100]}...")

        conn.commit()
        logger_instance.info(f"{env_name}成功将日志导入到数据库，共导入 {imported_count} 条记录，跳过 {skipped_count} 条")
    except Exception as e:
        logger_instance.error(f"{env_name}导入日志到数据库时出错: {str(e)}")
    finally:
        if conn:
            conn.close()

# 生产环境数据库导入函数
def import_logs_to_db():
    """生产环境日志导入"""
    import_logs_to_db_common(DATABASE_PATH, LOG_DIR, "kafka_data.log", "接收到消息", logger, "生产环境")

# 测试环境数据库导入函数
def import_logs_to_db_test():
    """测试环境日志导入"""
    import_logs_to_db_common(DATABASE_PATH_TEST, LOG_DIR_TEST, "kafka_data_test.log", "接收到测试消息", logger_test, "测试环境")

# 生产环境自动导入线程
def auto_import_thread():
    logger.info(f"生产环境自动导入线程已启动，间隔: {AUTO_IMPORT_INTERVAL}秒")
    while True:
        try:
            time.sleep(AUTO_IMPORT_INTERVAL)
            logger.info("执行生产环境自动导入...")
            import_logs_to_db()
        except Exception as e:
            logger.error(f"生产环境自动导入过程中出错: {str(e)}")

# 测试环境自动导入线程
def auto_import_thread_test():
    logger_test.info(f"测试环境自动导入线程已启动，间隔: {AUTO_IMPORT_INTERVAL}秒")
    while True:
        try:
            time.sleep(AUTO_IMPORT_INTERVAL)
            logger_test.info("执行测试环境自动导入...")
            import_logs_to_db_test()
        except Exception as e:
            logger_test.error(f"测试环境自动导入过程中出错: {str(e)}")

# 处理不同渠道消息的通用函数
def process_messages_for_mysql(rows, mysql_cursor, mysql_table, logger_instance, env_name=""):
    """
    处理消息并插入到MySQL的通用函数

    Args:
        rows: SQLite查询结果
        mysql_cursor: MySQL游标
        mysql_table: MySQL表名
        logger_instance: 日志记录器实例
        env_name: 环境名称（用于日志显示）

    Returns:
        tuple: (成功数量, 错误数量)
    """
    synced_count = 0
    error_count = 0

    for row in rows:
        try:
            # 解析JSON数据
            message_value = json.loads(row["value"])

            # 提取渠道
            channel = message_value.get("channel", "")

            # 根据不同渠道处理不同的数据结构
            if channel == "honor":
                # 荣耀渠道特殊处理
                data_obj = message_value.get("data", {})
                inner_data = data_obj.get("data", {})
                ad_page_response = inner_data.get("adPageResponse", {})
                campaigns_data = ad_page_response.get("data", [])

                # 处理每个广告创意
                for creative_data in campaigns_data:
                    # 从顶层获取创意信息
                    creative_id = creative_data.get("adCreativeId", "")
                    static_time = creative_data.get("staticTime", "")
                    time_period = creative_data.get("timePeriod", "")

                    # 从metrics中获取详细信息
                    metrics = creative_data.get("metrics", {})
                    campaign_id = metrics.get("adCampaignId", "")
                    campaign_name = metrics.get("adCampaignName", "")
                    creative_name = metrics.get("adCreativeName", "")
                    cost = float(metrics.get("adBilling", 0)) / 1000000  # 微转元
                    impression = int(metrics.get("impression", 0))
                    click = int(metrics.get("click", 0))

                    # 处理时间
                    stat_time = datetime.fromtimestamp(int(time_period)/1000).strftime('%Y-%m-%d %H:%M:%S') if time_period else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ds = static_time if static_time else datetime.now().strftime('%Y-%m-%d')

                    # 插入到MySQL
                    mysql_cursor.execute(f'''
                    INSERT IGNORE INTO {mysql_table}
                    (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ''', (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds))

                    if mysql_cursor.rowcount > 0:
                        synced_count += 1

            elif channel == "oppo":
                data_obj = message_value.get("data", {})
                # 支持两种数据格式：items（复数）和 item（单数）
                campaigns_data = data_obj.get("items", data_obj.get("item", []))

                # 处理每个广告创意
                for creative_data in campaigns_data:
                    creative_id = creative_data.get("ad_id")
                    creative_name = creative_data.get("ad_name")
                    campaign_id = creative_data.get("plan_id")
                    campaign_name = creative_data.get("plan_name")

                    # 费用处理：根据您的数据，cost字段直接是元，不需要转换
                    cost = float(creative_data.get("cost", 0))/100 # 分转元
                    impression = int(creative_data.get("expose", 0))
                    click = int(creative_data.get("click", 0))
                    download = int(creative_data.get("download", 0))

                    # 处理时间
                    ftime = creative_data.get("ftime")
                    if ftime:
                        try:
                            # 处理可能的浮点数格式，转换为整数
                            ftime_int = int(float(str(ftime)))
                            ds = datetime.strptime(str(ftime_int), "%Y%m%d").strftime('%Y-%m-%d')
                            stat_time = ds + " 00:00:00"
                        except (ValueError, TypeError) as e:
                            logger_instance.warning(f"{env_name}OPPO时间格式解析失败: {ftime}, 错误: {str(e)}")
                            ds = datetime.now().strftime('%Y-%m-%d')
                            stat_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        ds = datetime.now().strftime('%Y-%m-%d')
                        stat_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    # 插入到MySQL
                    mysql_cursor.execute(f'''
                    INSERT IGNORE INTO {mysql_table}
                    (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds,download)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ''', (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds, download))

                    if mysql_cursor.rowcount > 0:
                        synced_count += 1
            elif channel == 'huawei':
                data_obj = message_value.get("data", {})
                datas = data_obj.get("datas", [])

                # 处理华为渠道的每个数据项
                for item_data in datas:
                    try:
                        # 提取基本字段
                        creative_id = str(item_data.get('taskId', ''))
                        creative_name = f"华为任务_{creative_id}"  # 华为没有创意名称，生成一个
                        campaign_id = creative_id  # 华为使用taskId作为计划ID
                        campaign_name = f"华为计划_{creative_id}"  # 生成计划名称

                        # 提取数据字段，处理可能的null值
                        cost = float(item_data.get('cost') or 0)
                        impression = int(item_data.get('exposure') or 0)  # 华为用exposure表示曝光
                        click = int(item_data.get('click') or 0)
                        download = int(item_data.get('download') or 0)  # 华为有download字段

                        # 处理时间
                        stat_date = item_data.get('statDate')  # 格式: "2025-08-30"
                        if stat_date:
                            ds = stat_date
                            stat_time = stat_date + " 00:00:00"
                        else:
                            ds = datetime.now().strftime('%Y-%m-%d')
                            stat_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                        # 插入到MySQL
                        mysql_cursor.execute(f'''
                        INSERT IGNORE INTO {mysql_table}
                        (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds, download)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ''', (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds, download))

                        if mysql_cursor.rowcount > 0:
                            synced_count += 1

                    except Exception as e:
                        logger_instance.error(f"{env_name}处理华为数据项时出错: {str(e)}, 数据: {item_data}")
                        error_count += 1
                        continue

            else:
                # 其他渠道暂时不处理，但可以在这里添加新渠道的处理逻辑
                logger_instance.debug(f"{env_name}跳过未知渠道: {channel}")

        except Exception as e:
            logger_instance.error(f"{env_name}处理消息 {row['id']} 时出错: {str(e)}")
            error_count += 1
            continue

    return synced_count, error_count

# 通用的MySQL同步函数
def sync_to_mysql_common(database_path, mysql_table, logger_instance, env_name=""):
    """
    通用的MySQL同步函数

    Args:
        database_path: SQLite数据库路径
        mysql_table: MySQL目标表名
        logger_instance: 日志记录器实例
        env_name: 环境名称（用于日志显示）
    """
    if not MYSQL_SYNC_ENABLED:
        logger_instance.info(f"{env_name}MySQL同步未启用，跳过同步")
        return

    try:
        # 连接SQLite
        sqlite_conn = sqlite3.connect(database_path)
        sqlite_conn.row_factory = sqlite3.Row
        sqlite_cursor = sqlite_conn.cursor()

        # 获取未同步的记录
        sqlite_cursor.execute('''
        SELECT m.* FROM messages m
        LEFT JOIN synced_records s ON m.id = s.id
        WHERE s.id IS NULL OR s.synced = 0
        ''')

        rows = sqlite_cursor.fetchall()

        if not rows:
            logger_instance.info(f"{env_name}没有新数据需要同步到MySQL")
            return

        logger_instance.info(f"{env_name}开始同步 {len(rows)} 条记录到MySQL")

        # 连接MySQL
        mysql_conn = mysql.connector.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DATABASE
        )
        mysql_cursor = mysql_conn.cursor()

        synced_count = 0
        error_count = 0
        
        # 处理每条记录
        synced_count, error_count = process_messages_for_mysql(rows, mysql_cursor, mysql_table, logger_instance, env_name)

        # 标记已同步的记录
        for row in rows:
            try:
                sqlite_cursor.execute('''
                INSERT OR REPLACE INTO synced_records (id, synced)
                VALUES (?, 1)
                ''', (row["id"],))
            except Exception as e:
                logger_instance.error(f"{env_name}标记记录 {row['id']} 为已同步时出错: {str(e)}")
                #     # 其他渠道的处理逻辑
                #     data = message_value.get("data", {})
                    
                #     # 提取并转换字段
                #     campaign_id = data.get("adCampaignId", "")
                #     campaign_name = data.get("adCampaignName", "")
                #     creative_id = data.get("adCreativeId", "")
                #     creative_name = data.get("adCreativeName", "")
                #     cost = float(data.get("adBilling", 0)) / 1000000  # 微转元
                #     impression = int(data.get("impression", 0))
                #     click = int(data.get("click", 0))
                    
                #     # 处理时间字段
                #     time_period = data.get("timePeriod", "")
                #     static_time = data.get("staticTime", "")
                    
                #     if time_period:
                #         stat_time = datetime.fromtimestamp(int(time_period)/1000).strftime('%Y-%m-%d %H:%M:%S')
                #     else:
                #         stat_time = row["timestamp"]
                    
                #     # 使用staticTime作为日期，如果没有则从timePeriod生成
                #     if static_time:
                #         ds = static_time
                #     elif time_period:
                #         ds = datetime.fromtimestamp(int(time_period)/1000).strftime('%Y-%m-%d')
                #     else:
                #         ds = datetime.fromisoformat(row["timestamp"]).strftime('%Y-%m-%d')
                    
                #     # 插入到MySQL
                #     mysql_cursor.execute(f'''
                #     INSERT IGNORE INTO {MYSQL_TABLE}
                #     (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds)
                #     VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                #     ''', (channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds))
                    
                #     if mysql_cursor.rowcount > 0:
                #         synced_count += 1
                
                # 标记为已同步
                sqlite_cursor.execute('''
                INSERT OR REPLACE INTO synced_records (id, synced)
                VALUES (?, 1)
                ''', (row["id"],))
                
            except Exception as e:
                logger.error(f"处理消息 {row['id']} 时出错: {str(e)}")
                error_count += 1
                continue
        
        # 提交事务
        mysql_conn.commit()
        sqlite_conn.commit()

        logger_instance.info(f"{env_name}同步完成: 成功 {synced_count} 条, 失败 {error_count} 条")

    except Exception as e:
        logger_instance.error(f"{env_name}同步到MySQL过程中出错: {str(e)}")
    finally:
        if 'mysql_cursor' in locals() and mysql_cursor:
            mysql_cursor.close()
        if 'mysql_conn' in locals() and mysql_conn:
            mysql_conn.close()
        if 'sqlite_conn' in locals() and sqlite_conn:
            sqlite_conn.close()

# 生产环境MySQL同步函数
def sync_to_mysql():
    """生产环境MySQL同步"""
    sync_to_mysql_common(DATABASE_PATH, MYSQL_TABLE, logger, "生产环境")

# 测试环境MySQL同步函数
def sync_to_mysql_test():
    """测试环境MySQL同步"""
    sync_to_mysql_common(DATABASE_PATH_TEST, MYSQL_TABLE_TEST, logger_test, "测试环境")



# 生产环境MySQL同步线程函数
def mysql_sync_thread():
    logger.info(f"生产环境MySQL同步线程已启动，间隔: {MYSQL_SYNC_INTERVAL}秒")
    while True:
        try:
            time.sleep(MYSQL_SYNC_INTERVAL)
            logger.info("执行生产环境MySQL同步...")
            sync_to_mysql()
        except Exception as e:
            logger.error(f"生产环境MySQL同步过程中出错: {str(e)}")

# 测试环境MySQL同步线程函数
def mysql_sync_thread_test():
    logger_test.info(f"测试环境MySQL同步线程已启动，间隔: {MYSQL_SYNC_INTERVAL}秒")
    while True:
        try:
            time.sleep(MYSQL_SYNC_INTERVAL)
            logger_test.info("执行测试环境MySQL同步...")
            sync_to_mysql_test()
        except Exception as e:
            logger_test.error(f"测试环境MySQL同步过程中出错: {str(e)}")

# API路由
@get("/")
def home() -> Dict[str, str]:
    return {"status": "running", "service": "Kafka Data Service"}

@get("/import")
def trigger_import() -> Dict[str, str]:
    threading.Thread(target=import_logs_to_db).start()
    return {"status": "import_started"}

@get("/import-test")
def trigger_import_test() -> Dict[str, str]:
    threading.Thread(target=import_logs_to_db_test).start()
    return {"status": "test_import_started"}

@get("/stats")
def get_stats() -> Dict[str, Any]:
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM messages")
        count = cursor.fetchone()[0]
        
        cursor.execute("SELECT MAX(timestamp) FROM messages")
        last_message = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            "total_messages": count,
            "last_message_time": last_message
        }
    except Exception as e:
        return {"error": str(e)}

@get("/messages")
def get_messages() -> Dict[str, Any]:
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM messages ORDER BY id DESC LIMIT 10")
        rows = cursor.fetchall()
        
        # 将结果转换为字典列表
        messages = []
        for row in rows:
            message = {}
            for key in row.keys():
                message[key] = row[key]
            messages.append(message)
        
        conn.close()
        
        return {
            "messages": messages,
            "count": len(messages)
        }
    except Exception as e:
        return {"error": str(e)}

@get("/sync-mysql")
def trigger_mysql_sync() -> Dict[str, str]:
    threading.Thread(target=sync_to_mysql).start()
    return {"status": "mysql_sync_started"}

@get("/sync-mysql-test")
def trigger_mysql_sync_test() -> Dict[str, str]:
    threading.Thread(target=sync_to_mysql_test).start()
    return {"status": "test_mysql_sync_started"}

@get("/stats-test")
def get_stats_test() -> Dict[str, Any]:
    try:
        conn = sqlite3.connect(DATABASE_PATH_TEST)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM messages")
        count = cursor.fetchone()[0]

        cursor.execute("SELECT MAX(timestamp) FROM messages")
        last_message = cursor.fetchone()[0]

        conn.close()

        return {
            "total_messages": count,
            "last_message_time": last_message
        }
    except Exception as e:
        return {"error": str(e)}

@get("/messages-test")
def get_messages_test() -> Dict[str, Any]:
    try:
        conn = sqlite3.connect(DATABASE_PATH_TEST)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM messages ORDER BY id DESC LIMIT 10")
        rows = cursor.fetchall()

        # 将结果转换为字典列表
        messages = []
        for row in rows:
            message = {}
            for key in row.keys():
                message[key] = row[key]
            messages.append(message)

        conn.close()

        return {
            "messages": messages,
            "count": len(messages)
        }
    except Exception as e:
        return {"error": str(e)}

@get("/import-test-data")
def import_test_data() -> Dict[str, Any]:
    """导入测试数据到SQLite和MySQL"""
    try:
        # 连接SQLite
        sqlite_conn = sqlite3.connect(DATABASE_PATH)
        cursor = sqlite_conn.cursor()
        
        # 创建测试数据
        test_data = {
            "channel": "test_channel",
            "data": {
                "adCampaignId": "test_campaign_123",
                "adCampaignName": "测试广告计划",
                "adBilling": 1000000,  # 10元
                "impression": 1000,
                "click": 50,
                "timePeriod": str(int(time.time() * 1000))  # 当前时间戳（毫秒）
            }
        }
        
        # 插入到SQLite
        cursor.execute('''
        INSERT INTO messages (topic, partition, offset, key, value, timestamp, receive_time)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            KAFKA_TOPIC,
            0,
            0,
            "test_key",
            json.dumps(test_data),
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))
        
        sqlite_conn.commit()
        sqlite_conn.close()
        
        # 触发MySQL同步
        threading.Thread(target=sync_to_mysql).start()
        
        return {"status": "test_data_imported", "data": test_data}
    except Exception as e:
        return {"error": str(e)}

@get("/import-honor-test")
def import_honor_test() -> Dict[str, Any]:
    """导入荣耀渠道测试数据"""
    try:
        # 连接SQLite
        sqlite_conn = sqlite3.connect(DATABASE_PATH)
        cursor = sqlite_conn.cursor()
        
        # 创建荣耀渠道测试数据
        test_data = {
            "channel": "honor",
            "data": {
                "code": 0,
                "data": {
                    "adPageResponse": {
                        "data": [
                            {
                                "staticTime": "2025-07-13",
                                "timePeriod": 1752336000000,
                                "adCampaignId": "50024324785",
                                "metrics": {
                                    "adCampaignName": "车来了-搜索-ocpd激活",
                                    "adBilling": 1974339244,
                                    "impression": 13555,
                                    "adCampaignId": "50024324785",
                                    "click": 968
                                }
                            }
                        ]
                    }
                }
            },
            "dt": "2025-07-13~2025-07-13",
            "reqDate": "1752491214852"
        }
        
        # 插入到SQLite
        cursor.execute('''
        INSERT INTO messages (topic, partition, offset, key, value, timestamp, receive_time)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            KAFKA_TOPIC,
            0,
            0,
            "honor_test_key",
            json.dumps(test_data),
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))
        
        sqlite_conn.commit()
        sqlite_conn.close()
        
        # 触发MySQL同步
        threading.Thread(target=sync_to_mysql).start()
        
        return {"status": "honor_test_data_imported", "message": "荣耀渠道测试数据已导入"}
    except Exception as e:
        return {"error": str(e)}

# 应用初始化
def start_app():
    # 验证配置
    if not validate_config():
        logger.error("配置验证失败，应用启动中止")
        return None

    # 初始化生产环境SQLite数据库
    init_db()

    # 初始化测试环境SQLite数据库
    init_db_test()

    # 初始化生产环境MySQL数据库（如果启用）
    if MYSQL_SYNC_ENABLED:
        init_mysql()

    # 初始化测试环境MySQL数据库（如果启用）
    if MYSQL_SYNC_ENABLED:
        init_mysql_test()

    # 启动生产环境Kafka消费者线程
    kafka_thread = threading.Thread(target=kafka_consumer_thread, daemon=True)
    kafka_thread.start()

    # 启动测试环境Kafka消费者线程（如果配置了测试服务器）
    if KAFKA_BOOTSTRAP_SERVERS_TEST and KAFKA_BOOTSTRAP_SERVERS_TEST != ['']:
        kafka_thread_test = threading.Thread(target=kafka_consumer_thread_test, daemon=True)
        kafka_thread_test.start()
        logger.info("测试环境Kafka消费者线程已启动")

    # 如果启用了自动导入，启动生产环境自动导入线程
    if AUTO_IMPORT_ENABLED:
        auto_thread = threading.Thread(target=auto_import_thread, daemon=True)
        auto_thread.start()

    # 如果启用了自动导入，启动测试环境自动导入线程
    if AUTO_IMPORT_ENABLED and KAFKA_BOOTSTRAP_SERVERS_TEST and KAFKA_BOOTSTRAP_SERVERS_TEST != ['']:
        auto_thread_test = threading.Thread(target=auto_import_thread_test, daemon=True)
        auto_thread_test.start()
        logger_test.info("测试环境自动导入线程已启动")

    # 如果启用了MySQL同步，启动生产环境同步线程
    if MYSQL_SYNC_ENABLED:
        mysql_sync = threading.Thread(target=mysql_sync_thread, daemon=True)
        mysql_sync.start()

    # 如果启用了MySQL同步，启动测试环境同步线程
    if MYSQL_SYNC_ENABLED and KAFKA_BOOTSTRAP_SERVERS_TEST and KAFKA_BOOTSTRAP_SERVERS_TEST != ['']:
        mysql_sync_test = threading.Thread(target=mysql_sync_thread_test, daemon=True)
        mysql_sync_test.start()
        logger_test.info("测试环境MySQL同步线程已启动")

    # 创建并返回Starlite应用
    return Starlite(route_handlers=[
        home,
        trigger_import,
        trigger_import_test,
        get_stats,
        get_stats_test,
        get_messages,
        get_messages_test,
        trigger_mysql_sync,
        trigger_mysql_sync_test,
        import_test_data,
        import_honor_test
    ])

# 创建应用实例
app = start_app()

# 启动服务器
if __name__ == "__main__":
    import uvicorn

    # 获取服务配置
    host = os.getenv("SERVICE_HOST", "0.0.0.0")
    port = int(os.getenv("SERVICE_PORT", "8000"))

    logger.info(f"启动Web服务器 - 地址: {host}:{port}")

    # 启动uvicorn服务器
    uvicorn.run(app, host=host, port=port, log_level="info")