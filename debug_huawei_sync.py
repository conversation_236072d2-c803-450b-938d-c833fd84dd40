#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试华为渠道同步问题
"""

import sqlite3
import json
from datetime import datetime
from dotenv import load_dotenv
from app import (
    process_messages_for_mysql,
    DATABASE_PATH_TEST,
    MYSQL_TABLE_TEST,
    logger_test
)

# 加载环境变量
load_dotenv()

def debug_huawei_messages():
    """调试华为消息"""
    print("=== 调试华为消息 ===\n")
    
    try:
        # 连接测试数据库
        conn = sqlite3.connect(DATABASE_PATH_TEST)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 获取未同步的华为消息
        cursor.execute('''
        SELECT m.* FROM messages m
        LEFT JOIN synced_records s ON m.id = s.id
        WHERE m.value LIKE '%"channel": "huawei"%'
        AND (s.id IS NULL OR s.synced = 0)
        ORDER BY m.id DESC
        LIMIT 3
        ''')
        
        rows = cursor.fetchall()
        conn.close()
        
        if not rows:
            print("   ✗ 没有找到未同步的华为消息")
            
            # 检查所有华为消息
            conn = sqlite3.connect(DATABASE_PATH_TEST)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT m.id, m.key, s.synced FROM messages m
            LEFT JOIN synced_records s ON m.id = s.id
            WHERE m.value LIKE '%"channel": "huawei"%'
            ORDER BY m.id DESC
            LIMIT 5
            ''')
            
            all_rows = cursor.fetchall()
            conn.close()
            
            if all_rows:
                print("   所有华为消息状态:")
                for row in all_rows:
                    sync_status = "已同步" if row["synced"] == 1 else "未同步"
                    print(f"     ID: {row['id']}, Key: {row['key']}, 状态: {sync_status}")
            
            return False
        
        print(f"   ✓ 找到 {len(rows)} 条未同步的华为消息")
        
        # 分析每条消息
        for i, row in enumerate(rows, 1):
            print(f"\n--- 消息 {i} (ID: {row['id']}) ---")
            
            try:
                # 解析消息内容
                message_data = json.loads(row["value"])
                channel = message_data.get("channel")
                print(f"   渠道: {channel}")
                
                if channel != "huawei":
                    print(f"   ⚠️  渠道不是华为: {channel}")
                    continue
                
                # 检查数据结构
                data_obj = message_data.get("data", {})
                datas = data_obj.get("datas", [])
                print(f"   数据项数量: {len(datas)}")
                
                if not datas:
                    print("   ⚠️  没有datas数据")
                    continue
                
                # 分析每个数据项
                for j, item_data in enumerate(datas, 1):
                    print(f"   数据项 {j}:")
                    print(f"     taskId: {item_data.get('taskId')}")
                    print(f"     statDate: {item_data.get('statDate')}")
                    print(f"     cost: {item_data.get('cost')}")
                    print(f"     exposure: {item_data.get('exposure')}")
                    print(f"     click: {item_data.get('click')}")
                    print(f"     download: {item_data.get('download')}")
                
            except Exception as e:
                print(f"   ✗ 解析消息失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 调试华为消息失败: {str(e)}")
        return False

def test_huawei_processing_directly():
    """直接测试华为处理逻辑"""
    print("\n=== 直接测试华为处理逻辑 ===\n")
    
    try:
        # 连接测试数据库
        conn = sqlite3.connect(DATABASE_PATH_TEST)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 获取一条华为消息
        cursor.execute('''
        SELECT * FROM messages 
        WHERE value LIKE '%"channel": "huawei"%'
        ORDER BY id DESC
        LIMIT 1
        ''')
        
        row = cursor.fetchone()
        conn.close()
        
        if not row:
            print("   ✗ 没有找到华为消息")
            return False
        
        print(f"   ✓ 找到华为消息 (ID: {row['id']})")
        
        # 模拟MySQL游标，记录详细信息
        class DetailedMockCursor:
            def __init__(self):
                self.rowcount = 1
                self.executed_queries = []
                self.call_count = 0
            
            def execute(self, query, params):
                self.call_count += 1
                self.executed_queries.append((query, params))
                
                print(f"   → MySQL插入调用 #{self.call_count}:")
                print(f"     SQL: {query[:100]}...")
                print(f"     参数: {params}")
                
                # 检查参数数量
                expected_params = 11  # channel, campaign_id, campaign_name, creative_id, creative_name, cost, impression, click, stat_time, ds, download
                if len(params) != expected_params:
                    print(f"     ⚠️  参数数量不匹配: 期望{expected_params}, 实际{len(params)}")
        
        mock_cursor = DetailedMockCursor()
        
        # 调用处理函数
        print("   开始处理...")
        synced_count, error_count = process_messages_for_mysql(
            [row], mock_cursor, MYSQL_TABLE_TEST, logger_test, "调试环境"
        )
        
        print(f"\n   处理结果:")
        print(f"     成功: {synced_count} 条")
        print(f"     失败: {error_count} 条")
        print(f"     MySQL调用次数: {mock_cursor.call_count}")
        
        if mock_cursor.call_count == 0:
            print("   ❌ 没有调用MySQL插入，说明华为处理逻辑没有被执行")
        elif error_count > 0:
            print("   ❌ 处理过程中有错误")
        else:
            print("   ✅ 处理成功")
        
        return mock_cursor.call_count > 0
        
    except Exception as e:
        print(f"   ✗ 直接测试失败: {str(e)}")
        return False

def check_sync_records():
    """检查同步记录"""
    print("\n=== 检查同步记录 ===\n")
    
    try:
        conn = sqlite3.connect(DATABASE_PATH_TEST)
        cursor = conn.cursor()
        
        # 检查华为消息的同步状态
        cursor.execute('''
        SELECT m.id, m.key, s.synced, m.timestamp
        FROM messages m
        LEFT JOIN synced_records s ON m.id = s.id
        WHERE m.value LIKE '%"channel": "huawei"%'
        ORDER BY m.id DESC
        LIMIT 10
        ''')
        
        rows = cursor.fetchall()
        conn.close()
        
        if not rows:
            print("   ✗ 没有找到华为消息")
            return
        
        print(f"   ✓ 找到 {len(rows)} 条华为消息:")
        for row in rows:
            sync_status = "已同步" if row[2] == 1 else "未同步" if row[2] == 0 else "无记录"
            print(f"     ID: {row[0]}, Key: {row[1]}, 状态: {sync_status}, 时间: {row[3]}")
        
    except Exception as e:
        print(f"   ✗ 检查同步记录失败: {str(e)}")

def main():
    """主函数"""
    print("=== 华为渠道同步问题调试 ===\n")
    
    # 检查同步记录
    check_sync_records()
    
    # 调试华为消息
    has_unsynced = debug_huawei_messages()
    
    # 直接测试处理逻辑
    processing_works = test_huawei_processing_directly()
    
    print("\n=== 调试总结 ===")
    if not has_unsynced:
        print("❌ 问题: 没有未同步的华为消息，可能已经被错误地标记为已同步")
    elif not processing_works:
        print("❌ 问题: 华为处理逻辑没有被执行，可能是渠道判断有问题")
    else:
        print("✅ 华为处理逻辑正常，问题可能在其他地方")
    
    print("\n建议:")
    print("1. 检查华为消息的channel字段是否正确")
    print("2. 检查process_messages_for_mysql函数中的华为分支")
    print("3. 检查是否有异常被捕获但没有记录")
    print("4. 手动重置同步状态进行重试")

if __name__ == "__main__":
    main()
