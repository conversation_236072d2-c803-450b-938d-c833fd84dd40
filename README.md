# Kafka Data Service

一个用于消费Kafka消息并同步到MySQL数据库的服务。

## 功能特性

- 🔄 实时消费Kafka消息
- 💾 自动存储到SQLite本地数据库
- 🔄 定时同步到MySQL数据库
- 📊 支持多种渠道数据格式（荣耀、其他渠道）
- 🌐 提供REST API接口
- 📝 完整的日志记录
- ⚙️ 灵活的配置管理
- 🚀 systemd服务支持（开机自启、自动重启）
- 🛡️ 安全配置管理（敏感信息隔离）
- 📋 便捷的服务管理脚本
- 🧪 **支持生产环境和测试环境并行运行**

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

复制配置模板并填写实际配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填写以下必要配置：

```bash
# 生产环境Kafka配置
KAFKA_BOOTSTRAP_SERVERS=your_kafka_servers
KAFKA_TOPIC=your_topic_name

# 测试环境Kafka配置（可选）
KAFKA_BOOTSTRAP_SERVERS_TEST=your_test_kafka_servers

# MySQL配置（敏感信息）
MYSQL_HOST=your_mysql_host
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=your_database_name
MYSQL_TABLE=channel_data
MYSQL_TABLE_TEST=channel_data_test
```

⚠️ **重要**：确保 `.env` 文件权限安全：
```bash
chmod 600 .env
```

### 3. 安装systemd服务（推荐）

安装为系统服务，支持开机自启和自动重启：

```bash
# 安装systemd服务
sudo ./install-service.sh
```

### 4. 启动服务

```bash
# 使用systemctl管理（推荐）
sudo systemctl start kafka-data-service
sudo systemctl enable kafka-data-service  # 开机自启

# 或使用便捷管理脚本
sudo ./service-manager.sh start

# 传统方式（不推荐）
python app.py
nohup python app.py > app.log 2>&1 &
```

服务将在 `http://0.0.0.0:8000` 启动。

## API 接口

### 生产环境API
| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 服务状态检查 |
| `/stats` | GET | 获取生产环境消息统计信息 |
| `/messages` | GET | 获取生产环境最近10条消息 |
| `/import` | GET | 手动触发生产环境日志导入 |
| `/sync-mysql` | GET | 手动触发生产环境MySQL同步 |

### 测试环境API
| 端点 | 方法 | 描述 |
|------|------|------|
| `/stats-test` | GET | 获取测试环境消息统计信息 |
| `/messages-test` | GET | 获取测试环境最近10条消息 |
| `/import-test` | GET | 手动触发测试环境日志导入 |
| `/sync-mysql-test` | GET | 手动触发测试环境MySQL同步 |

### 测试数据API
| 端点 | 方法 | 描述 |
|------|------|------|
| `/import-test-data` | GET | 导入测试数据 |
| `/import-honor-test` | GET | 导入荣耀渠道测试数据 |

## 配置说明

### 必需配置

- `KAFKA_BOOTSTRAP_SERVERS`: Kafka服务器地址
- `MYSQL_HOST`: MySQL服务器地址
- `MYSQL_USER`: MySQL用户名
- `MYSQL_PASSWORD`: MySQL密码
- `MYSQL_DATABASE`: MySQL数据库名

### 可选配置

- `KAFKA_TOPIC`: Kafka主题名（默认：ad_channel_data_sync）
- `KAFKA_GROUP_ID`: 消费者组ID（默认：starlite-service-group）
- `AUTO_IMPORT_ENABLED`: 是否启用自动导入（默认：true）
- `AUTO_IMPORT_INTERVAL`: 自动导入间隔秒数（默认：300）
- `MYSQL_SYNC_ENABLED`: 是否启用MySQL同步（默认：true）
- `MYSQL_SYNC_INTERVAL`: MySQL同步间隔秒数（默认：600）

### 测试环境配置（可选）

如果需要同时运行测试环境，可以配置以下参数：

- `KAFKA_BOOTSTRAP_SERVERS_TEST`: 测试环境Kafka服务器地址
- `LOG_DIR_TEST`: 测试环境日志目录（默认：logs_test）
- `DATABASE_PATH_TEST`: 测试环境SQLite数据库路径（默认：kafka_data_test.db）
- `MYSQL_TABLE_TEST`: 测试环境MySQL表名（默认：channel_data_test）

**注意**: 测试环境配置是完全可选的。如果不配置 `KAFKA_BOOTSTRAP_SERVERS_TEST`，系统将只运行生产环境。

## 安全注意事项

⚠️ **重要**:
- 不要将 `.env` 文件提交到版本控制系统
- 定期更换数据库密码
- 在生产环境中使用强密码
- 考虑使用密钥管理服务

## 日志

日志文件位于 `logs/` 目录：
- `kafka_data.log`: 主要服务日志
- 日志自动按天轮转，保留7天

## 服务管理

### systemctl命令（推荐）

```bash
# 启动服务
sudo systemctl start kafka-data-service

# 停止服务
sudo systemctl stop kafka-data-service

# 重启服务
sudo systemctl restart kafka-data-service

# 查看服务状态
sudo systemctl status kafka-data-service

# 查看实时日志
sudo journalctl -u kafka-data-service -f

# 启用开机自启
sudo systemctl enable kafka-data-service

# 禁用开机自启
sudo systemctl disable kafka-data-service
```

### 便捷管理脚本

```bash
# 查看帮助
./service-manager.sh help

# 启动服务
sudo ./service-manager.sh start

# 停止服务
sudo ./service-manager.sh stop

# 重启服务
sudo ./service-manager.sh restart

# 查看状态
sudo ./service-manager.sh status

# 查看实时日志
sudo ./service-manager.sh logs

# 启用/禁用开机自启
sudo ./service-manager.sh enable
sudo ./service-manager.sh disable
```

### 服务卸载

```bash
# 卸载systemd服务
sudo ./uninstall-service.sh

# 或使用管理脚本
sudo ./service-manager.sh uninstall
```

## 故障排除

### 常见问题

1. **Kafka连接失败**
   - 检查 `KAFKA_BOOTSTRAP_SERVERS` 配置
   - 确认Kafka服务器可访问

2. **MySQL连接失败**
   - 检查MySQL配置信息
   - 确认数据库权限

3. **没有接收到消息**
   - 检查Kafka主题是否存在
   - 确认消费者组配置

4. **服务启动失败**
   - 检查配置文件 `.env` 是否正确
   - 查看服务日志：`sudo journalctl -u kafka-data-service -n 50`
   - 确认Python环境和依赖包

### 查看日志

```bash
# systemd服务日志（推荐）
sudo journalctl -u kafka-data-service -f

# 应用日志文件
tail -f logs/kafka_data.log

# 启动日志
tail -f app.log
```

## 数据库结构

### MySQL (channel_data表)
- id: 主键
- channel: 渠道
- campaign_id: 广告计划ID
- campaign_name: 广告计划名称
- cost: 成本（元）
- impression: 展示次数
- click: 点击次数
- stat_time: 统计时间
- ds: 日期
- create_time: 创建时间
- update_time: 更新时间

## 项目结构

```
.
├── app.py                      # 主应用文件
├── .env                        # 环境配置（不提交）
├── .env.example                # 配置模板
├── .gitignore                  # Git忽略文件
├── README.md                   # 项目说明
├── requirements.txt            # Python依赖
├── kafka-data-service.service  # systemd服务配置
├── install-service.sh          # 服务安装脚本
├── uninstall-service.sh        # 服务卸载脚本
├── service-manager.sh          # 服务管理脚本（推荐）
├── kafka-service.sh            # 简单服务管理脚本（备选）
├── logs/                       # 日志目录
│   └── kafka_data.log         # 应用日志
├── app.log                     # 启动日志
└── kafka_data.db              # SQLite数据库
```

## 部署建议

### 生产环境部署

1. **使用systemd服务**（推荐）
   ```bash
   sudo ./install-service.sh
   sudo systemctl enable kafka-data-service
   ```

2. **配置日志轮转**
   - systemd会自动管理日志
   - 应用日志在 `logs/` 目录下自动轮转

3. **监控和告警**
   - 使用 `systemctl status` 监控服务状态
   - 配置日志监控告警
   - 监控磁盘空间和内存使用

4. **安全设置**
   - 定期更换数据库密码
   - 限制文件权限：`chmod 600 .env`
   - 考虑使用专用用户运行服务

### 开发环境

```bash
# 直接运行（开发调试）
python app.py

# 或使用简单脚本
./kafka-service.sh start
```

## 高级配置

### systemd服务配置

服务配置文件 `kafka-data-service.service` 包含以下特性：

- **自动重启**：服务异常退出时自动重启
- **资源限制**：限制文件描述符和进程数
- **安全设置**：启用安全沙箱功能
- **日志管理**：集成systemd日志系统

### 自定义服务配置

如需修改服务配置，编辑 `kafka-data-service.service` 文件：

```bash
# 修改用户（如果需要）
User=your_user
Group=your_group

# 修改Python路径
ExecStart=/path/to/your/python /data/script/ls/channel_data/app.py

# 修改工作目录
WorkingDirectory=/your/app/directory
```

修改后重新安装服务：
```bash
sudo ./uninstall-service.sh
sudo ./install-service.sh
```

### 监控和维护

```bash
# 查看服务资源使用
systemctl show kafka-data-service --property=MainPID --value | xargs -I {} ps -p {} -o pid,ppid,pcpu,pmem,cmd

# 查看服务启动时间
systemctl show kafka-data-service --property=ActiveEnterTimestamp

# 查看服务重启次数
systemctl show kafka-data-service --property=NRestarts

# 重置失败计数
sudo systemctl reset-failed kafka-data-service
```

## 测试环境详细说明

### 双环境架构

系统现在支持生产环境和测试环境并行运行：

```
生产环境数据流:
测试Kafka → 生产日志 → 生产SQLite → 生产MySQL表

测试环境数据流:
测试Kafka → 测试日志 → 测试SQLite → 测试MySQL表
```

### 目录结构

```
项目根目录/
├── logs/                    # 生产环境日志目录
│   └── kafka_data.log      # 生产环境日志文件
├── logs_test/              # 测试环境日志目录
│   └── kafka_data_test.log # 测试环境日志文件
├── kafka_data.db           # 生产环境SQLite数据库
├── kafka_data_test.db      # 测试环境SQLite数据库
├── test_config.py          # 配置测试脚本
├── test_start.py           # 启动测试脚本
├── test_api.py             # API测试脚本
└── 测试环境使用说明.md      # 详细使用说明
```

### 测试脚本

```bash
# 测试配置是否正确
python test_config.py

# 测试应用启动
python test_start.py

# 测试API接口（需要先启动应用）
python test_api.py
```

### 监控命令

```bash
# 查看生产环境日志
tail -f logs/kafka_data.log

# 查看测试环境日志
tail -f logs_test/kafka_data_test.log

# 查看生产环境数据库
sqlite3 kafka_data.db "SELECT COUNT(*) FROM messages;"

# 查看测试环境数据库
sqlite3 kafka_data_test.db "SELECT COUNT(*) FROM messages;"
```

### 注意事项

1. **独立运行**: 两套环境完全独立，互不影响
2. **可选配置**: 测试环境配置是可选的，不配置则只运行生产环境
3. **资源消耗**: 同时运行两套环境会增加系统资源消耗
4. **数据隔离**: 确保测试数据不会影响生产数据

详细使用说明请参考 `测试环境使用说明.md` 文件。