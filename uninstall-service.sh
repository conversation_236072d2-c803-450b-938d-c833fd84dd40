#!/bin/bash

# Kafka Data Service 卸载脚本
# 用于从systemd中移除服务

set -e

SERVICE_NAME="kafka-data-service"
SERVICE_FILE="${SERVICE_NAME}.service"
SYSTEMD_DIR="/etc/systemd/system"

echo "🗑️  开始卸载 Kafka Data Service..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 停止服务
echo "🛑 停止服务..."
if systemctl is-active --quiet $SERVICE_NAME 2>/dev/null; then
    systemctl stop $SERVICE_NAME
    echo "✅ 服务已停止"
else
    echo "ℹ️  服务未运行"
fi

# 禁用服务
echo "⚡ 禁用服务..."
if systemctl is-enabled --quiet $SERVICE_NAME 2>/dev/null; then
    systemctl disable $SERVICE_NAME
    echo "✅ 服务已禁用"
else
    echo "ℹ️  服务未启用"
fi

# 删除服务文件
echo "🗑️  删除服务文件..."
if [ -f "$SYSTEMD_DIR/$SERVICE_FILE" ]; then
    rm "$SYSTEMD_DIR/$SERVICE_FILE"
    echo "✅ 服务文件已删除"
else
    echo "ℹ️  服务文件不存在"
fi

# 重新加载systemd
echo "🔄 重新加载systemd..."
systemctl daemon-reload
systemctl reset-failed

echo "✅ Kafka Data Service 已完全卸载！"
echo ""
echo "ℹ️  注意：应用程序文件和数据未被删除，只是移除了systemd服务"
