#!/bin/bash

# Kafka Data Service 简单管理脚本
# 不依赖systemd的服务管理

SERVICE_NAME="kafka-data-service"
APP_DIR="/data/script/ls/channel_data"
APP_FILE="app.py"
PID_FILE="$APP_DIR/kafka-service.pid"
LOG_FILE="$APP_DIR/app.log"
PYTHON_PATH="/root/anaconda3/bin/python"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查进程是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 启动服务
start() {
    echo -e "${BLUE}🚀 启动 $SERVICE_NAME...${NC}"
    
    if is_running; then
        echo -e "${YELLOW}⚠️  服务已在运行中${NC}"
        return 1
    fi
    
    cd "$APP_DIR"
    nohup "$PYTHON_PATH" "$APP_FILE" > "$LOG_FILE" 2>&1 &
    local pid=$!
    echo $pid > "$PID_FILE"
    
    sleep 2
    if is_running; then
        echo -e "${GREEN}✅ 服务启动成功 (PID: $pid)${NC}"
        return 0
    else
        echo -e "${RED}❌ 服务启动失败${NC}"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服务
stop() {
    echo -e "${BLUE}🛑 停止 $SERVICE_NAME...${NC}"
    
    if ! is_running; then
        echo -e "${YELLOW}⚠️  服务未运行${NC}"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    kill "$pid"
    
    # 等待进程结束
    local count=0
    while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 10 ]; do
        sleep 1
        count=$((count + 1))
    done
    
    if ps -p "$pid" > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  强制终止进程${NC}"
        kill -9 "$pid"
    fi
    
    rm -f "$PID_FILE"
    echo -e "${GREEN}✅ 服务已停止${NC}"
}

# 重启服务
restart() {
    echo -e "${BLUE}🔄 重启 $SERVICE_NAME...${NC}"
    stop
    sleep 2
    start
}

# 查看状态
status() {
    echo -e "${BLUE}📊 $SERVICE_NAME 状态:${NC}"
    
    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo -e "${GREEN}✅ 服务正在运行 (PID: $pid)${NC}"
        
        # 显示进程信息
        echo -e "${BLUE}📈 进程信息:${NC}"
        ps -p "$pid" -o pid,ppid,pcpu,pmem,etime,cmd --no-headers
        
        # 显示端口监听情况
        echo -e "${BLUE}🌐 端口监听:${NC}"
        netstat -tlnp 2>/dev/null | grep ":8000 " || echo "端口8000未监听"
        
    else
        echo -e "${RED}❌ 服务未运行${NC}"
    fi
}

# 查看日志
logs() {
    echo -e "${BLUE}📝 查看日志 (按 Ctrl+C 退出):${NC}"
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        echo -e "${RED}❌ 日志文件不存在: $LOG_FILE${NC}"
    fi
}

# 显示帮助
help() {
    echo -e "${BLUE}$SERVICE_NAME 管理脚本${NC}"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|help}"
    echo ""
    echo "命令:"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  restart - 重启服务"
    echo "  status  - 查看服务状态"
    echo "  logs    - 查看实时日志"
    echo "  help    - 显示此帮助信息"
}

# 主逻辑
case "${1:-help}" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    help|--help|-h)
        help
        ;;
    *)
        echo -e "${RED}❌ 未知命令: $1${NC}"
        help
        exit 1
        ;;
esac
