[Unit]
Description=Kafka Data Service - 渠道数据采集服务
Documentation=https://tx.git.chelaile.net.cn/chelaile_analysis/channel_data
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/data/script/ls/channel_data
Environment=PATH=/root/anaconda3/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/root/anaconda3/bin/python /data/script/ls/channel_data/app.py
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/data/script/ls/channel_data
ReadWritePaths=/data/script/ls/channel_data/logs
ReadWritePaths=/data/script/ls/channel_data/kafka_data.db

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
