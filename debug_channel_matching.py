#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试渠道匹配问题
"""

import sqlite3
import json
from datetime import datetime

def debug_channel_matching():
    """调试渠道匹配"""
    print("=== 调试渠道匹配 ===\n")
    
    try:
        # 连接测试数据库
        conn = sqlite3.connect('kafka_data_test.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 获取最新的华为消息
        cursor.execute('''
        SELECT m.* FROM messages m
        LEFT JOIN synced_records s ON m.id = s.id
        WHERE m.value LIKE '%huawei%'
        AND (s.id IS NULL OR s.synced = 0)
        ORDER BY m.id DESC
        LIMIT 1
        ''')
        
        row = cursor.fetchone()
        conn.close()
        
        if not row:
            print("   ✗ 没有找到未同步的华为消息")
            return
        
        print(f"   ✓ 找到华为消息 (ID: {row['id']})")
        
        # 解析消息
        message_data = json.loads(row["value"])
        channel = message_data.get("channel")
        
        print(f"   渠道值: '{channel}'")
        print(f"   渠道类型: {type(channel)}")
        print(f"   渠道长度: {len(channel) if channel else 'None'}")
        print(f"   渠道repr: {repr(channel)}")
        
        # 测试各种匹配条件
        conditions = [
            ('channel == "huawei"', channel == "huawei"),
            ("channel == 'huawei'", channel == 'huawei'),
            ('channel.strip() == "huawei"', channel.strip() == "huawei" if channel else False),
            ('channel.lower() == "huawei"', channel.lower() == "huawei" if channel else False),
        ]
        
        print("\n   匹配测试:")
        for condition, result in conditions:
            status = "✓" if result else "✗"
            print(f"     {status} {condition}: {result}")
        
        # 模拟处理逻辑
        print("\n   模拟处理逻辑:")
        if channel == "honor":
            print("     → 进入荣耀渠道处理")
        elif channel == "oppo":
            print("     → 进入OPPO渠道处理")
        elif channel == "huawei":
            print("     → 进入华为渠道处理 ✓")
        else:
            print(f"     → 跳过未知渠道: {channel}")
        
    except Exception as e:
        print(f"   ✗ 调试失败: {str(e)}")

def test_direct_processing():
    """直接测试处理逻辑"""
    print("\n=== 直接测试处理逻辑 ===\n")
    
    # 创建测试数据
    test_message = {
        "channel": "huawei",
        "data": {
            "datas": [
                {
                    "taskId": 123456789,
                    "statDate": "2025-09-01",
                    "cost": 100.0,
                    "exposure": 1000,
                    "click": 50,
                    "download": 25
                }
            ]
        }
    }
    
    print(f"   测试消息: {json.dumps(test_message, indent=2)}")
    
    # 模拟处理逻辑
    channel = test_message.get("channel", "")
    print(f"   提取的渠道: '{channel}'")
    
    synced_count = 0
    error_count = 0
    
    if channel == "honor":
        print("   → 荣耀渠道处理")
    elif channel == "oppo":
        print("   → OPPO渠道处理")
    elif channel == "huawei":
        print("   → 华为渠道处理 ✓")
        
        data_obj = test_message.get("data", {})
        datas = data_obj.get("datas", [])
        
        print(f"   数据项数量: {len(datas)}")
        
        for item_data in datas:
            try:
                creative_id = str(item_data.get('taskId', ''))
                creative_name = f"华为任务_{creative_id}"
                campaign_id = creative_id
                campaign_name = f"华为计划_{creative_id}"
                
                cost = float(item_data.get('cost') or 0)
                impression = int(item_data.get('exposure') or 0)
                click = int(item_data.get('click') or 0)
                download = int(item_data.get('download') or 0)
                
                stat_date = item_data.get('statDate')
                if stat_date:
                    ds = stat_date
                    stat_time = stat_date + " 00:00:00"
                else:
                    ds = datetime.now().strftime('%Y-%m-%d')
                    stat_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                print(f"   处理结果:")
                print(f"     任务ID: {creative_id}")
                print(f"     费用: {cost}, 曝光: {impression}, 点击: {click}, 下载: {download}")
                print(f"     时间: {stat_time}")
                
                synced_count += 1
                
            except Exception as e:
                print(f"   ✗ 处理数据项失败: {str(e)}")
                error_count += 1
    else:
        print(f"   → 跳过未知渠道: '{channel}'")
    
    print(f"\n   处理结果: 成功 {synced_count} 条, 失败 {error_count} 条")

def check_app_code():
    """检查app.py中的华为处理代码"""
    print("\n=== 检查app.py中的华为处理代码 ===\n")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找华为相关的代码
        lines = content.split('\n')
        huawei_lines = []
        
        for i, line in enumerate(lines, 1):
            if 'huawei' in line.lower():
                huawei_lines.append((i, line.strip()))
        
        if huawei_lines:
            print("   找到华为相关代码:")
            for line_num, line in huawei_lines:
                print(f"     第{line_num}行: {line}")
        else:
            print("   ✗ 没有找到华为相关代码")
        
        # 检查渠道判断条件
        if 'elif channel == "huawei":' in content:
            print("\n   ✓ 找到华为渠道判断条件: elif channel == \"huawei\":")
        elif "elif channel == 'huawei':" in content:
            print("\n   ✓ 找到华为渠道判断条件: elif channel == 'huawei':")
        else:
            print("\n   ✗ 没有找到华为渠道判断条件")
        
    except Exception as e:
        print(f"   ✗ 检查代码失败: {str(e)}")

def main():
    """主函数"""
    print("=== 华为渠道匹配问题调试 ===\n")
    
    # 检查app.py代码
    check_app_code()
    
    # 调试渠道匹配
    debug_channel_matching()
    
    # 直接测试处理逻辑
    test_direct_processing()
    
    print("\n=== 调试总结 ===")
    print("如果华为渠道匹配正常但仍然没有数据入库，可能的原因:")
    print("1. 应用需要重启才能使代码修改生效")
    print("2. MySQL连接或表结构有问题")
    print("3. 异常被捕获但没有记录到日志")
    print("4. 事务提交有问题")

if __name__ == "__main__":
    main()
