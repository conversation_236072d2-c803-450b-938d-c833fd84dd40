#!/bin/bash

# Kafka Data Service 安装脚本
# 用于将服务注册为systemd服务

set -e

SERVICE_NAME="kafka-data-service"
SERVICE_FILE="${SERVICE_NAME}.service"
SYSTEMD_DIR="/etc/systemd/system"
CURRENT_DIR=$(pwd)

echo "🚀 开始安装 Kafka Data Service..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 检查服务文件是否存在
if [ ! -f "$SERVICE_FILE" ]; then
    echo "❌ 服务文件 $SERVICE_FILE 不存在"
    exit 1
fi

# 停止现有服务（如果正在运行）
echo "🛑 停止现有服务..."
if systemctl is-active --quiet $SERVICE_NAME 2>/dev/null; then
    systemctl stop $SERVICE_NAME
    echo "✅ 已停止现有服务"
fi

# 停止可能运行的手动进程
echo "🔍 检查并停止手动启动的进程..."
PIDS=$(pgrep -f "python.*app.py" || true)
if [ ! -z "$PIDS" ]; then
    echo "发现运行中的进程: $PIDS"
    kill $PIDS || true
    sleep 2
    # 强制杀死如果还在运行
    PIDS=$(pgrep -f "python.*app.py" || true)
    if [ ! -z "$PIDS" ]; then
        kill -9 $PIDS || true
    fi
    echo "✅ 已停止手动进程"
fi

# 复制服务文件
echo "📋 复制服务文件..."
cp "$SERVICE_FILE" "$SYSTEMD_DIR/"
echo "✅ 服务文件已复制到 $SYSTEMD_DIR/"

# 设置权限
chmod 644 "$SYSTEMD_DIR/$SERVICE_FILE"

# 重新加载systemd
echo "🔄 重新加载systemd..."
systemctl daemon-reload

# 启用服务
echo "⚡ 启用服务..."
systemctl enable $SERVICE_NAME

# 启动服务
echo "🚀 启动服务..."
systemctl start $SERVICE_NAME

# 检查服务状态
sleep 3
if systemctl is-active --quiet $SERVICE_NAME; then
    echo "✅ 服务安装成功并已启动！"
    echo ""
    echo "📋 常用命令："
    echo "  启动服务: systemctl start $SERVICE_NAME"
    echo "  停止服务: systemctl stop $SERVICE_NAME"
    echo "  重启服务: systemctl restart $SERVICE_NAME"
    echo "  查看状态: systemctl status $SERVICE_NAME"
    echo "  查看日志: journalctl -u $SERVICE_NAME -f"
    echo "  禁用服务: systemctl disable $SERVICE_NAME"
    echo ""
    echo "🔍 当前服务状态："
    systemctl status $SERVICE_NAME --no-pager -l
else
    echo "❌ 服务启动失败，请检查日志："
    echo "journalctl -u $SERVICE_NAME --no-pager -l"
    exit 1
fi
