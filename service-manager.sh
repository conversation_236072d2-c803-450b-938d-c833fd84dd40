#!/bin/bash

# Kafka Data Service 管理脚本
# 提供便捷的服务管理命令

SERVICE_NAME="kafka-data-service"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Kafka Data Service 管理工具${NC}"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  status    查看服务状态"
    echo "  logs      查看实时日志"
    echo "  enable    启用开机自启"
    echo "  disable   禁用开机自启"
    echo "  install   安装systemd服务"
    echo "  uninstall 卸载systemd服务"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动服务"
    echo "  $0 logs     # 查看日志"
    echo "  $0 status   # 查看状态"
}

# 检查服务是否存在
check_service_exists() {
    if ! systemctl list-unit-files | grep -q "^${SERVICE_NAME}.service"; then
        echo -e "${RED}❌ 服务未安装，请先运行: $0 install${NC}"
        exit 1
    fi
}

# 启动服务
start_service() {
    check_service_exists
    echo -e "${BLUE}🚀 启动服务...${NC}"
    if systemctl start $SERVICE_NAME; then
        echo -e "${GREEN}✅ 服务启动成功${NC}"
        sleep 2
        systemctl status $SERVICE_NAME --no-pager -l
    else
        echo -e "${RED}❌ 服务启动失败${NC}"
        exit 1
    fi
}

# 停止服务
stop_service() {
    check_service_exists
    echo -e "${BLUE}🛑 停止服务...${NC}"
    if systemctl stop $SERVICE_NAME; then
        echo -e "${GREEN}✅ 服务已停止${NC}"
    else
        echo -e "${RED}❌ 服务停止失败${NC}"
        exit 1
    fi
}

# 重启服务
restart_service() {
    check_service_exists
    echo -e "${BLUE}🔄 重启服务...${NC}"
    if systemctl restart $SERVICE_NAME; then
        echo -e "${GREEN}✅ 服务重启成功${NC}"
        sleep 2
        systemctl status $SERVICE_NAME --no-pager -l
    else
        echo -e "${RED}❌ 服务重启失败${NC}"
        exit 1
    fi
}

# 查看服务状态
show_status() {
    check_service_exists
    echo -e "${BLUE}📊 服务状态:${NC}"
    systemctl status $SERVICE_NAME --no-pager -l
    echo ""
    echo -e "${BLUE}📈 资源使用情况:${NC}"
    systemctl show $SERVICE_NAME --property=MainPID --value | xargs -I {} ps -p {} -o pid,ppid,pcpu,pmem,cmd --no-headers 2>/dev/null || echo "进程信息不可用"
}

# 查看日志
show_logs() {
    check_service_exists
    echo -e "${BLUE}📝 实时日志 (按 Ctrl+C 退出):${NC}"
    journalctl -u $SERVICE_NAME -f --no-pager
}

# 启用开机自启
enable_service() {
    check_service_exists
    echo -e "${BLUE}⚡ 启用开机自启...${NC}"
    if systemctl enable $SERVICE_NAME; then
        echo -e "${GREEN}✅ 开机自启已启用${NC}"
    else
        echo -e "${RED}❌ 启用开机自启失败${NC}"
        exit 1
    fi
}

# 禁用开机自启
disable_service() {
    check_service_exists
    echo -e "${BLUE}⚡ 禁用开机自启...${NC}"
    if systemctl disable $SERVICE_NAME; then
        echo -e "${GREEN}✅ 开机自启已禁用${NC}"
    else
        echo -e "${RED}❌ 禁用开机自启失败${NC}"
        exit 1
    fi
}

# 安装服务
install_service() {
    if [ ! -f "install-service.sh" ]; then
        echo -e "${RED}❌ install-service.sh 文件不存在${NC}"
        exit 1
    fi
    bash install-service.sh
}

# 卸载服务
uninstall_service() {
    if [ ! -f "uninstall-service.sh" ]; then
        echo -e "${RED}❌ uninstall-service.sh 文件不存在${NC}"
        exit 1
    fi
    bash uninstall-service.sh
}

# 主逻辑
case "${1:-help}" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    enable)
        enable_service
        ;;
    disable)
        disable_service
        ;;
    install)
        install_service
        ;;
    uninstall)
        uninstall_service
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}❌ 未知命令: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
