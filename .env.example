# ================================
# Kafka 配置
# ================================
# Kafka服务器地址列表，用逗号分隔
KAFKA_BOOTSTRAP_SERVERS=localhost:9092

# Kafka主题名称
KAFKA_TOPIC=ad_channel_data_sync

# Kafka消费者组ID
KAFKA_GROUP_ID=starlite-service-group

# 偏移重置策略: earliest, latest, none
KAFKA_AUTO_OFFSET_RESET=earliest

# Kafka安全协议: PLAINTEXT, SSL, SASL_PLAINTEXT, SASL_SSL
KAFKA_SECURITY_PROTOCOL=PLAINTEXT

# ================================
# 日志配置
# ================================
# 日志文件存储目录
LOG_DIR=logs

# 日志文件保留天数
LOG_RETENTION_DAYS=7

# ================================
# SQLite 数据库配置
# ================================
# SQLite数据库文件路径
DATABASE_PATH=kafka_data.db

# ================================
# 自动导入配置
# ================================
# 是否启用自动导入: true, false
AUTO_IMPORT_ENABLED=true

# 自动导入间隔（秒）
AUTO_IMPORT_INTERVAL=300

# ================================
# MySQL 数据库配置（请填写实际值）
# ================================
# MySQL服务器地址
MYSQL_HOST=your_mysql_host

# MySQL端口
MYSQL_PORT=3306

# MySQL用户名
MYSQL_USER=your_mysql_user

# MySQL密码
MYSQL_PASSWORD=your_mysql_password

# MySQL数据库名
MYSQL_DATABASE=your_database_name

# MySQL表名
MYSQL_TABLE=channel_data

# ================================
# MySQL同步配置
# ================================
# 是否启用MySQL同步: true, false
MYSQL_SYNC_ENABLED=true

# MySQL同步间隔（秒）
MYSQL_SYNC_INTERVAL=600

# ================================
# 数据保留配置
# ================================
# 数据保留天数
DATA_RETENTION_DAYS=30

# 清理间隔（小时）
CLEANUP_INTERVAL_HOURS=24

# ================================
# Web服务配置
# ================================
# 服务监听地址
SERVICE_HOST=0.0.0.0

# 服务监听端口
SERVICE_PORT=8000
